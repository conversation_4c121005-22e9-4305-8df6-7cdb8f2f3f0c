// In-page floating status overlay for Approval RPA
// Injected as a content script; UI isolated with Shadow DOM

(() => {
  if (window.__approvalRpaOverlayInjected) return;
  window.__approvalRpaOverlayInjected = true;

  // DOM setup: host + shadow root
  const host = document.createElement('div');
  host.id = 'approval-rpa-overlay-root';
  host.style.position = 'fixed';
  host.style.zIndex = '2147483646';
  host.style.right = '12px';
  host.style.bottom = '12px';
  host.style.width = '360px';
  host.style.maxHeight = '70vh';
  host.style.pointerEvents = 'none'; // only the shadow content will be interactive

  const shadow = host.attachShadow({ mode: 'open' });

  const style = document.createElement('style');
  style.textContent = `
    :host { all: initial; }
    *, *::before, *::after { box-sizing: border-box; }
    .overlay { font-family: ui-sans-serif, system-ui, -apple-system, Segoe UI, Roboto, Helvetica, <PERSON><PERSON>, "Noto Sans", "Apple Color Emoji", "Segoe UI Emoji"; color: #0f172a; }
    .container { pointer-events: auto; background: #fff; border: 1px solid #e2e8f0; border-radius: 10px; box-shadow: 0 8px 24px rgba(2,6,23,.15); overflow: hidden; }
    .header { display: flex; align-items: center; gap: 8px; background: #112d51; color: #fff; padding: 8px 10px; }
    .brand { display: flex; align-items: center; gap: 6px; font-weight: 600; font-size: 13px; letter-spacing: .2px; }
    .brand-badge { display:inline-flex; align-items:center; justify-content:center; width:18px; height:18px; border-radius:4px; background: rgba(255,255,255,.2); font-size:12px; }
    .spacer { flex: 1; }
    .status-pill { display: inline-flex; align-items: center; gap: 6px; font-size: 12px; padding: 2px 8px; border-radius: 999px; background: rgba(255,255,255,.2); }
    .toggle { border: none; background: transparent; color: #fff; font-size: 16px; cursor: pointer; padding: 2px 4px; border-radius: 4px; }
    .toggle:hover { background: rgba(255,255,255,.15); }
    .icon-btn { border: none; background: transparent; color: #fff; font-size: 16px; cursor: pointer; padding: 2px 6px; border-radius: 4px; }
    .icon-btn:hover { background: rgba(255,255,255,.15); }

    .body { padding: 10px; display: grid; grid-template-rows: auto auto 1fr; gap: 10px; }
    .section { border: 1px solid #e2e8f0; border-radius: 8px; padding: 8px; }
    .section h4 { margin: 0 0 6px; font-size: 12px; color: #334155; font-weight: 600; }
    .controls { display: flex; gap: 8px; }
    .controls button { border: 1px solid #cbd5e1; background: #fff; color: #112d51; border-radius: 6px; padding: 6px 10px; font-size: 12px; cursor: pointer; }
    .controls button:disabled { opacity: .6; cursor: not-allowed; }


    .tasks ul { list-style: none; margin: 0; padding: 0; }
    .tasks li { display: flex; align-items: center; gap: 8px; padding: 6px; border-radius: 6px; font-size: 12px; }
    .tasks li.pending { color: #475569; }
    .tasks li.completed { color: #16a34a; background: #ecfdf5; }
    .tasks li.running { color: #0284c7; background: #e0f2fe; }
    .tasks li.paused { color: #f59e0b; background: #fffbeb; }
    .tasks li.waiting { color: #7c3aed; background: #f5f3ff; }
    .tasks li.error { color: #dc2626; background: #fef2f2; }
    .tasks li.warning { color: #b45309; background: #fff7ed; }
    .status-icon { width: 16px; height: 16px; display: inline-flex; align-items: center; justify-content: center; }

    table { width: 100%; border-collapse: collapse; font-size: 12px; }
    th, td { padding: 6px; border-bottom: 1px solid #e2e8f0; vertical-align: top; }
    thead th { position: sticky; top: 0; background: #f8fafc; z-index: 1; }
    .vars { max-height: 150px; overflow: auto; }
    .view-link { color: #0369a1; text-decoration: underline; cursor: pointer; }

    .warnings ul { margin: 0; padding-left: 16px; max-height: 120px; overflow:auto; }
    .warnings li { margin: 4px 0; }
    .warning-msg { font-weight: 600; color: #b45309; }
    .warning-details { white-space: pre-wrap; background: #fff7ed; border: 1px dashed #fdba74; border-radius: 4px; padding: 6px; color: #7c2d12; }

    .error { background: #fef2f2; border: 1px solid #fecaca; border-radius: 8px; padding: 8px; }
    .error .message { color: #b91c1c; font-weight: 700; margin-bottom: 6px; }
    .error pre { max-height: 120px; overflow: auto; white-space: pre-wrap; background: #fff; border: 1px dashed #fecaca; padding: 6px; border-radius: 4px; }

    .completion { background: #ecfdf5; border: 1px solid #bbf7d0; border-radius: 8px; padding: 8px; color: #14532d; }

    .collapsed .body { display: none; }

    /* Modal */
    .modal { position: fixed; inset: 0; display: none; align-items: center; justify-content: center; background: rgba(2,6,23,.35); z-index: 10; }
    .modal.open { display: flex; }
    .modal-card { width: 80vw; max-width: 900px; max-height: 80vh; background: #fff; border-radius: 10px; box-shadow: 0 10px 30px rgba(2,6,23,.4); overflow: hidden; display: flex; flex-direction: column; }
    .modal-head { display:flex; align-items:center; justify-content: space-between; padding: 8px 12px; background: #0ea5e9; color: #fff; }
    .modal-body { padding: 12px; overflow: auto; }
    .modal-body pre { white-space: pre-wrap; }
    .close-btn { border:none; background: rgba(255,255,255,.2); color:#fff; padding:4px 8px; border-radius:6px; cursor:pointer; }
    .close-btn:hover { background: rgba(255,255,255,.3); }
  `;

  const container = document.createElement('div');
  container.className = 'overlay container';
  container.innerHTML = `
    <div class="header">
      <span class="brand"><img id="brand-icon" class="brand-badge" alt="AR" /> 审批自动化</span>
      <span id="status-pill" class="status-pill">状态: idle</span>
      <span class="spacer"></span>
      <button id="toggle-btn" class="icon-btn" title="折叠/展开">▾</button>
      <button id="close-btn" class="icon-btn" title="关闭">×</button>
    </div>
    <div class="body">
      <div class="section controls">
        <button id="overlay-start">启动</button>
        <button id="overlay-pause" disabled>暂停</button>
        <button id="overlay-stop" disabled>终止</button>
      </div>
      <div class="section tasks">
        <h4>任务进度</h4>
        <div id="task-list"></div>
      </div>

      <div id="warnings-section" class="section warnings" style="display:none;">
        <h4>警告</h4>
        <ul id="warnings-list"></ul>
      </div>
      <div id="error-section" class="section error" style="display:none;">
        <div class="message" id="error-message"></div>
        <pre id="error-details"></pre>
      </div>
      <div id="completion-section" class="section completion" style="display:none;">
        <div id="completion-content"></div>
      </div>
    </div>
    <div id="detail-modal" class="modal">
      <div class="modal-card">
        <div class="modal-head">
          <span id="modal-title">详细内容</span>
          <button id="modal-close" class="close-btn">关闭</button>
        </div>
        <div id="modal-body" class="modal-body"></div>
      </div>
    </div>
  `;

  shadow.appendChild(style);
  shadow.appendChild(container);
  document.documentElement.appendChild(host);
  // set icon.png as brand badge
  try {
    const brandIcon = shadow.getElementById('brand-icon');
    if (brandIcon) brandIcon.src = chrome.runtime.getURL('icons/icon.png');
  } catch (_) {}


  // UI references inside shadow
  const statusPill = shadow.getElementById('status-pill');
  const toggleBtn = shadow.getElementById('toggle-btn');
  const taskList = shadow.getElementById('task-list');

  const warningsSection = shadow.getElementById('warnings-section');
  const warningsList = shadow.getElementById('warnings-list');
  const errorSection = shadow.getElementById('error-section');
  const errorMessage = shadow.getElementById('error-message');
  const errorDetails = shadow.getElementById('error-details');
  const completionSection = shadow.getElementById('completion-section');
  const completionContent = shadow.getElementById('completion-content');
  const modal = shadow.getElementById('detail-modal');
  const modalClose = shadow.getElementById('modal-close');
  const startBtn = shadow.getElementById('overlay-start');
  const pauseBtn = shadow.getElementById('overlay-pause');
  const stopBtn = shadow.getElementById('overlay-stop');
  const closeBtn = shadow.getElementById('close-btn');


  // Icons from popup for consistency
  const icons = {
    pending: `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16"><path d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14zm0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16z"/></svg>`,
    completed: `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16"><path d="M16 8A8 8 0 1 1 0 8a8 8 0 0 1 16 0zm-3.97-3.03a.75.75 0 0 0-1.08.022L7.477 9.417 5.384 7.323a.75.75 0 0 0-1.06 1.06L6.97 11.03a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 0 0-.01-1.05z"/></svg>`,
    running: `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16"><path d="M8 0a8 8 0 1 1 0 16A8 8 0 0 1 8 0zM4.5 7.5a.5.5 0 0 0 0 1h5.793l-2.147 2.146a.5.5 0 0 0 .708.708l3-3a.5.5 0 0 0 0-.708l-3-3a.5.5 0 1 0-.708.708L10.293 7.5H4.5z"/></svg>`,
    waiting: `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16"><path d="M16 8A8 8 0 1 1 0 8a8 8 0 0 1 16 0zM6.25 5C5.56 5 5 5.56 5 6.25v3.5a1.25 1.25 0 1 0 2.5 0v-3.5C7.5 5.56 6.94 5 6.25 5zm3.5 0c-.69 0-1.25.56-1.25 1.25v3.5a1.25 1.25 0 1 0 2.5 0v-3.5C11 5.56 10.44 5 9.75 5z"/></svg>`,
    waiting_user: `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16"><path d="M3 14s-1 0-1-1 1-4 6-4 6 3 6 4-1 1-1 1H3zm5-6a3 3 0 1 0 0-6 3 3 0 0 0 0 6z"/></svg>`,
    paused: `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16"><path d="M16 8A8 8 0 1 1 0 8a8 8 0 0 1 16 0zM6.25 5C5.56 5 5 5.56 5 6.25v3.5a1.25 1.25 0 1 0 2.5 0v-3.5C7.5 5.56 6.94 5 6.25 5zm3.5 0c-.69 0-1.25.56-1.25 1.25v3.5a1.25 1.25 0 1 0 2.5 0v-3.5C11 5.56 10.44 5 9.75 5z"/></svg>`,
    error: `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16"><path d="M16 8A8 8 0 1 1 0 8a8 8 0 0 1 16 0zM8 4a.905.905 0 0 0-.9.995l.35 3.507a.552.552 0 0 0 1.1 0l.35-3.507A.905.905 0 0 0 8 4zm.002 6a1 1 0 1 0 0 2 1 1 0 0 0 0-2z"/></svg>`,
    warning: `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16"><path d="M8.982 1.566a1.13 1.13 0 0 0-1.96 0L.165 13.233c-.457.778.091 1.767.98 1.767h13.713c.889 0 1.438-.99.98-1.767L8.982 1.566zM8 5c.535 0 .954.462.9.995l-.35 3.507a.552.552 0 0 1-1.1 0L7.1 5.995A.905.905 0 0 1 8 5zm.002 6a1 1 0 1 1 0 2 1 1 0 0 1 0-2z"/></svg>`
  };

  function formatStepDetails(step) {
    if (!step) return '';
    if (step.description) return step.description;
    let details = step.command || '';
    if (step.selector) {
      if (typeof step.selector === 'object') {
        details += ` by ${step.selector.by}: ${step.selector.value}`;
      } else {
        details += `: ${step.selector}`;
      }
    } else if (step.url) {
      details += `: ${step.url}`;
    } else if (step.variableName) {
      details += `: ${step.variableName}`;
    } else if (step.expression) {
      details += `: ${step.expression}`;
    } else if (step.condition) {
      details += `: ${step.condition}`;
    } else if (step.label) {
      details = `Label: ${step.label}`;
    }
    return details || '(未命名步骤)';
  }



  function setStatusPill(status) {
    const map = {
      running: { text: '运行中', icon: icons.running },
      paused: { text: '已暂停', icon: icons.paused },
      waiting_for_approval: { text: '等待确认', icon: icons.waiting },
      waiting_for_user_input: { text: '等待用户', icon: icons.waiting_user },
      error: { text: '错误', icon: icons.error },
      idle: { text: '空闲', icon: icons.pending }
    };
    const cfg = map[status] || map.idle;
    statusPill.innerHTML = `<span class="status-icon">${cfg.icon}</span><span>状态: ${cfg.text}</span>`;

    // Update control buttons state
    startBtn.disabled = status !== 'idle';
    pauseBtn.disabled = !(status === 'running' || status === 'paused');
    pauseBtn.textContent = status === 'paused' ? '继续' : '暂停';
    stopBtn.disabled = status === 'idle';
  }

  let __pollTimer = null;
  function schedulePoll(status) {
    const shouldPoll = status === 'running';
    if (!shouldPoll) {
      if (__pollTimer) { clearInterval(__pollTimer); __pollTimer = null; }
      return;
    }
    if (!__pollTimer) {
      __pollTimer = setInterval(() => {
        chrome.runtime.sendMessage({ command: 'getStatus' }, (resp) => {
          if (resp) updateUI(resp);
        });
      }, 800);
    }
  }

  function updateUI(statusInfo) {
    const { status, step: currentStep, script, errorInfo, warnings, completionMessage, showWarnings } = statusInfo;

    setStatusPill(status);

    // Variables section removed intentionally

    // Warnings
    warningsList.innerHTML = '';
    if (showWarnings && warnings && warnings.length > 0) {
      warningsSection.style.display = 'block';
      warnings.forEach(w => {
        const li = document.createElement('li');
        const msg = document.createElement('div'); msg.className = 'warning-msg'; msg.textContent = `${w.message}`; li.appendChild(msg);
        const pre = document.createElement('pre'); pre.className = 'warning-details'; pre.textContent = JSON.stringify(w.details, null, 2); li.appendChild(pre);
        warningsList.appendChild(li);
      });
    } else {
      warningsSection.style.display = 'none';
    }

    // Error
    errorSection.style.display = 'none';
    if (status === 'error' && errorInfo) {
      errorSection.style.display = 'block';
      errorMessage.textContent = errorInfo.message || '未知错误';
      errorDetails.textContent = errorInfo.details ? JSON.stringify(errorInfo.details, null, 2) : '无详细信息';
    }

    // Completion
    if (completionMessage) {
      completionSection.style.display = 'block';
      completionContent.textContent = completionMessage;
    } else {
      completionSection.style.display = 'none';
    }

    // Tasks
    if (script && script.length > 0) {
      // preserve scroll position
      const prevScrollTop = taskList.scrollTop;
      const prevScrollHeight = taskList.scrollHeight;

      const ul = document.createElement('ul');
      let currentItemTop = null;

      script.forEach((step, index) => {
        const li = document.createElement('li');
        let icon = icons.pending;
        let liClass = 'pending';
        const hasWarning = warnings && warnings.some(w => w.step === index);
        if (status === 'error' && index === (errorInfo && errorInfo.step)) {
          liClass = 'error'; icon = icons.error;
        } else if (index < currentStep) {
          liClass = 'completed'; icon = icons.completed; if (hasWarning) { liClass = 'warning'; icon = icons.warning; }
        } else if (index === currentStep) {
          switch (status) {
            case 'running': liClass = 'running'; icon = icons.running; break;
            case 'paused': liClass = 'paused'; icon = icons.paused; break;
            case 'waiting_for_approval': liClass = 'waiting'; icon = icons.waiting; break;
            case 'waiting_for_user_input': liClass = 'waiting'; icon = icons.waiting_user; break;
          }
          currentItemTop = index; // mark current index
        }
        li.className = liClass;
        li.innerHTML = `<span class="status-icon">${icon}</span><span>${formatStepDetails(step)}</span>`;
        ul.appendChild(li);
      });

      taskList.innerHTML = '';
      taskList.appendChild(ul);

      const userNearTop = prevScrollTop <= 5;
      const userNearBottom = (prevScrollTop + taskList.clientHeight) >= (prevScrollHeight - 5);

      if (currentItemTop !== null) {
        // auto-scroll to keep current step visible if user wasn't actively viewing a different zone
        if (userNearTop || userNearBottom) {
          const target = ul.children[currentItemTop];
          if (target) {
            const top = target.offsetTop - 20;
            taskList.scrollTo({ top, behavior: 'instant' });
          }
        } else {
          // preserve user's reading position
          taskList.scrollTop = prevScrollTop;
        }
      } else {
        taskList.scrollTop = prevScrollTop;
      }
    } else {
      taskList.textContent = '没有要执行的任务。';
    }

    schedulePoll(status);
  }

  // Events
  toggleBtn.addEventListener('click', (e) => {
    e.stopPropagation();
    const isCollapsed = container.classList.toggle('collapsed');
    toggleBtn.textContent = isCollapsed ? '▸' : '▾';
  });

  modalClose.addEventListener('click', () => {
    modal.classList.remove('open');
  });

  // Header buttons
  startBtn.addEventListener('click', () => {
    chrome.runtime.sendMessage({ command: 'start', isAutoApprove: true });
  });
  pauseBtn.addEventListener('click', () => {
    const isContinue = pauseBtn.textContent === '继续';
    chrome.runtime.sendMessage({ command: isContinue ? 'resume' : 'pause' });
  });
  stopBtn.addEventListener('click', () => {
    chrome.runtime.sendMessage({ command: 'stop' });
  });
  closeBtn.addEventListener('click', () => {
    host.remove();
    window.__approvalRpaOverlayInjected = false; // allow re-inject later
  });

  modal.addEventListener('click', (e) => {
    if (e.target === modal) {
      modal.classList.remove('open');
    }
  });

  // Listen for background status updates
  chrome.runtime.onMessage.addListener((request) => {
    if (request && request.type === 'statusUpdate') {
      updateUI(request);
    }
  });

  // Default visibility behavior: show overlay only when status is not idle
  const originalSetStatusPill = setStatusPill;
  setStatusPill = function(status) {
    originalSetStatusPill(status);
    container.style.display = (status && status !== 'idle') ? 'block' : 'none';
  };

  // Initial load (overlay hidden by default until script is not idle)
  container.style.display = 'none';
  chrome.runtime.sendMessage({ command: 'getStatus' }, (response) => {
    if (response) {
      updateUI(response);
    }
  });


})();

